version: "3.9"
services:
  tensorrt_llm-dev:
    image: ${DEV_CONTAINER_IMAGE}
    network_mode: host
    ipc: host

    # For GPU usage and profiling
    cap_add:
      - SYS_PTRACE
      - SYS_ADMIN
    security_opt:
      - seccomp:unconfined

    # Delete this section if you don't have a GPU.
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: "all"
              capabilities: [gpu]

    volumes:
      - ${SOURCE_DIR}:/workspaces/tensorrt_llm
      - ${LOCAL_HF_HOME}:/huggingface  # HF cache

    environment:
      - CCACHE_DIR=/workspaces/tensorrt_llm/cpp/.ccache
      - CCACHE_BASEDIR=/workspaces/tensorrt_llm

    # Overrides default command so things don't shut down after the process ends.
    command: /bin/sh -c "while sleep 1000; do :; done"
