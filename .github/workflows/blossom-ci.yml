# SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A workflow to trigger ci on hybrid infra (github + self hosted runner)
name: Blossom-CI
on:
  issue_comment:
    types: [created]
  workflow_dispatch:
      inputs:
          platform:
            description: 'runs-on argument'
            required: false
          args:
            description: 'argument'
            required: false

jobs:
  Authorization:
    name: Authorization
    runs-on: blossom
    outputs:
      args: ${{ env.args }}

    # This job only runs for pull request comments
    if: |
        (startsWith(github.event.comment.body, '/bot run') ||
        startsWith(github.event.comment.body, '/bot skip --comment') ||
        startsWith(github.event.comment.body, '/bot reuse-pipeline') ||
        startsWith(github.event.comment.body, '/bot kill')) && contains(
        fromJson('["byshiue","chuangz0","funatiq","hypdeb","jdemouth-nvidia","joyang-nv","lowsfer","Tabrizian","yweng0828","Shixiaowei02","MartinMarciniszyn","schetlur-nv","dcampora","pcastonguay","Naveassaf","lfr-0531","nekorobov","PerkzZheng","kaiyux","nv-guomingz","LinPoly","thorjohnsen","jiahanc","latency1024","tburt-nv","zeroepoch","chzblych","niukuo","ZhanruiSunCh","EmmaQiaoCh","yiqingy0","achartier","suyoggupta","amukkara","mk-nvidia","QiJune","lucaslie","davidmlw","hlu1","nvzhou","syuoni","NVGaryJi","symphonylyh","hello-11","zongfeijing","Jackch-NV","jinyangyuan-nvidia","LarryXFly","crazydemo","jaedeok-nvidia","wm2012011492","rosenrodt","zhuoyao1012","xinhe-nv","Yuening-wa","Shunkangz","zhengd-nv","yibinl-nvidia","StanleySun639","KingsleyLiu-NV","kxdc","yingcanw","BestJuly","ChristinaZ","bobboli","xueweilnvidia","kunlunl","cherichy","lucifer1004","Autumn1998","litaotju","peaceh-nv","liji-nv","SimengLiu-nv","yuxianq","yechank-nvidia","vallis-neria","DylanChen-NV","Tracin","zhhuang-nv","ISEEKYAN","xupinjie","tongyuantongyu","laikhtewari","zhuolingwang","dominicshanshan","jershi425","shifangx","StudyingShao","Superjomn","dongjiyingdjy","guangyunh-nv","wili-65535","tiffany940107","DanBlanaru","mikeiovine","djns99","ruodil","xiaoweiw-nv","xuwchen","bashimao","yizhang-nv","hyukn","nvpohanh","yuki-666","juney-nvidia","barry-delaney","Kefeng-Duan","MinaHuai","yilin-void","jhaotingc","jmydurant","katec846","CarstyYou","Njuapp","Jie-Fang","nvbrantz","inocsin","ruoqianguo","chenfeiz0326","ming-wei","eopXD","longlee0622","dongfengy","georgeliu95","evezhier","rakib-hasan","shangz-ai","JyChang012","wangsiping1997","yuanjings-nvda","tomeras91","roikoren755","amirkl94","shaharmor98","danielafrimi","amitz-nv","hijkzzz","rzilberstein-nvidia","dc3671","hchings","yuhengxnv","dongxuy04","qiaoxj07","omera-nv","DomBrown","brb-nv","FrankD412","yuhsuan-t","Fridah-nv","a-mccarthy","HuiGao-NV","alexmsettle","meenchen","sugunav14","cjluo-nv","kyleliang-nv","chang-l","WeiHaocheng","qixiang-99","BatshevaBlack","ebarilanM","xmchen1987","lingjiew","heyuhhh","netanel-haber","jiefangz-nv","wyw1267","yunruis","sklevtsov-nvidia","jgangani","pamelap-nvidia","ixlmar","GalSha","Dido0o0","rabiel","nvzhihanj","milesial","fzmu727","zackyoray","RoeyAzran1992","viraatc","v-shobhit","yuanjingx87","uchihatmtkinu","nvrohanv","vegaluisjose","qsang-nv","ChunhuanLin","timlee0212","venkywonka","zbpatel","tijyojwad","shyeh25","zihaok","nv-yilinf","ttyio","farazkh80","yuantailing","JennyLiu-nv","moraxu","IzzyPutterman","nvchenghaoz","nvxuanyuc","poweiw","stnie","zhanga5","nzmora-nvidia","greg-kwasniewski1","linda-stadter","Tom-Zheng","vanshilshah97","ixlmar","MatthiasKohl","Wanli-Jiang", "arekay", "davidclark-nv", "2ez4bz", "tcherckez-nvidia", "MrGeva", "galagam", "limin2021", "dhansen-nvidia","talorabr","kanghui0204","wu6u3tw","hvagadia","xavier-nvidia","raayandhar","dbari","nvjullin","elvischenv","zhenhuaw-me","weireweire","yifeizhang-c","jiaganc","ziyixiong-nv","FelixXidddd","JunyiXu-nv","bo-nv","zerollzeng","RayenTian","ameynaik-hub","raymochen","shuyixiong","johncalesp","leslie-fang25","reasonsolo","zhou-yuxin","vadiklyutiy","yali-arch","NVShreyas","h-guo18","pengbowang-nv","lancelly","heyuhhh","mayani-nv","flin3500","sunnyqgg","kris1025", "karljang", "ajrasane", "jthomson04", "fredricz-20070104", "aalanwyr", "samuellees", "nvamyt", "jinzh-nvidia", "zheyuf", "yumin066", "sychen52", "xxi-nv", "barneuman", "xuanzic", "yufeiwu-nv", "richardhuo-nv", "dcaox", "tshmilnvidia"]'),
        github.actor)
    steps:
      - name: Check if comment is issued by authorized person
        run: blossom-ci
        env:
          OPERATION: 'AUTH'
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_KEY_DATA: ${{ secrets.BLOSSOM_KEY }}

  Vulnerability-scan:
    name: Vulnerability scan
    needs: [Authorization]
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          repository: ${{ fromJson(needs.Authorization.outputs.args).repo }}
          ref: ${{ fromJson(needs.Authorization.outputs.args).ref }}
          lfs: 'true'

      - name: Run blossom action
        uses: NVIDIA/blossom-action@main
        env:
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_KEY_DATA: ${{ secrets.BLOSSOM_KEY }}
        with:
          args1: ${{ fromJson(needs.Authorization.outputs.args).args1 }}
          args2: ${{ fromJson(needs.Authorization.outputs.args).args2 }}
          args3: ${{ fromJson(needs.Authorization.outputs.args).args3 }}

  Job-trigger:
    name: Start ci job
    needs: [Vulnerability-scan]
    runs-on: blossom
    steps:
      - name: Start ci job
        run: blossom-ci
        env:
          OPERATION: 'START-CI-JOB'
          CI_SERVER: ${{ secrets.CI_SERVER }}
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  Upload-Log:
    name: Upload log
    runs-on: blossom
    if : github.event_name == 'workflow_dispatch'
    steps:
      - name: Jenkins log for pull request ${{ fromJson(github.event.inputs.args).pr }} (click here)
        run: blossom-ci
        env:
          OPERATION: 'POST-PROCESSING'
          CI_SERVER: ${{ secrets.CI_SERVER }}
          REPO_TOKEN: ${{ secrets.GITHUB_TOKEN }}
