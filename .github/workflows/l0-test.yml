# SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# A workflow to trigger ci on hybrid infra (github + self hosted runner)
name: L0-Test
on:
  workflow_dispatch:
      inputs:
          sha:
            description: 'commit sha'
            required: true
          test_result:
            description: 'test result'
            required: false
          test_results_url:
            description: 'test results url'
            required: true
jobs:
  Upload-Test:
    name: Upload test results
    runs-on: linux-amd64-cpu4
    if: github.event_name == 'workflow_dispatch'
    steps:
      - name: Update commit status
        uses: actions/github-script@v6
        with:
          script: |
            state = 'pending'
            description = 'collecting test results'
            if ('${{ github.event.inputs.test_result }}' == 'success') {
              state = 'success'
              description = 'test passed, collecting test results'
            }
            github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              context: 'blossom-ci',
              sha: '${{ github.event.inputs.sha }}',
              target_url: 'https://github.com/NVIDIA/TensorRT-LLM/actions/runs/' + context.runId,
              state: state,
              description: description,
            })
      - name: Collect test result
        run: rm -rf results && mkdir results && cd results && curl --user svc_tensorrt:${{ secrets.ARTIFACTORY_TOKEN }} -L ${{ github.event.inputs.test_results_url }} | tar -xz
      - name: Create test summary
        id: test_summary
        uses: test-summary/action@dist
        with:
          paths: results/**/results*.xml
      - name: Update commit status
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              context: 'blossom-ci',
              sha: '${{ github.event.inputs.sha }}',
              target_url: 'https://github.com/NVIDIA/TensorRT-LLM/actions/runs/' + context.runId,
              state: '${{ steps.test_summary.outputs.failed > 0 && 'failure' || github.event.inputs.test_result || 'success' }}',
              description: '${{ steps.test_summary.outputs.passed }} passed, ${{ steps.test_summary.outputs.failed }} failed, ${{ steps.test_summary.outputs.skipped }} skipped',
            })
