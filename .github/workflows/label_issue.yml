name: Label New Issues

on:
  issues:
    types: [opened]

permissions:
  issues: write
  contents: read

jobs:
  label-issue:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout private action repository
        uses: actions/checkout@v4
        with:
          repository: poweiw/goggles_action
          path: ./.github/actions/goggles_action # local path to store the action
          token: ${{ secrets.GOGGLES_ACTION_REPO_TOKEN}} # token to access poweiw/goggles_action
          ref: v1.2.1

      - name: AI Label Issue
        uses: ./.github/actions/goggles_action/actions/llm_label
        with:
          ACTION_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          LLM_MODEL_NAME: ${{ secrets.GOGGLES_LLM_MODEL_NAME }}
          LLM_TOKEN_SERVER_URL: ${{ secrets.GOGGLES_LLM_TOKEN_SERVER_URL }}
          LLM_TOKEN_CLIENT_ID: ${{ secrets.GOGGLES_LLM_TOKEN_CLIENT_ID }}
          LLM_TOKEN_CLIENT_SECRET: ${{ secrets.GOGGLES_LLM_TOKEN_CLIENT_SECRET }}
          LLM_GENERATE_URL: ${{ secrets.GOGGLES_LLM_GENERATE_URL }}
          LLM_TOKEN_SCOPE: ${{ secrets.GOGGLES_LLM_TOKEN_SCOPE }}
          REPO_OWNER: ${{ github.repository_owner }}
          REPO_NAME: ${{ github.event.repository.name }}
          ISSUE_NUMBER: ${{ github.event.issue.number }}
          ISSUE_TITLE: ${{ github.event.issue.title }}
          ISSUE_BODY: ${{ github.event.issue.body }}
          GITHUB_API_URL: ${{ github.api_url }}
          ACTIONS_STEP_VERBOSE: false
          EXCLUDED_LABELS: "bug,Community want to contribute,Community Engagement,duplicate,help wanted,Investigating,need more info,question,roadmap,stale,waiting for feedback,wontfix"
          LLM_SYSTEM_PROMPT: |
            You are an expert GitHub issue labeler. Your task is to analyze the provided issue title, issue body, and a list of available labels with their descriptions.
            Based on this information, select the single most appropriate label from the list that best captures the primary issue or request.
            Prefer selecting only one label that represents the main topic or problem. Only suggest multiple labels if the issue genuinely spans multiple distinct areas that are equally important.
            Respond with ONLY the chosen label name (e.g., 'bug', 'feature-request') or comma-separated names if multiple are truly needed.
            If no labels seem appropriate, respond with 'NONE'.
            If the issue is a bug (the 'bug' label may be applied separately and is excluded here), output ONLY the applicable secondary labels from this exact allowlist: Customized kernels, Decoding, Disaggregated serving, Doc, Frontend, General perf, Inference runtime, Infra, LLM API, Model customization, Model optimization, OOTB, Pytorch, Scale-out, Speculative Decoding, Testing, Triton backend, Windows. Use exact casing; if none apply, respond with 'NONE'. Do not output 'bug'.
            Do not add any other text, explanation, or markdown formatting.
