# SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: PR Checks

on:
  pull_request:
    types: [opened, edited, synchronize, reopened]

jobs:
  check-pr-title:
    name: Check PR Title Format
    runs-on: ubuntu-latest
    steps:
      - name: Validate PR Title Format
        id: check-pr-title
        uses: agenthunt/conventional-commit-checker-action@v2.0.0
        continue-on-error: true
        with:
          pr-title-regex: "^(\\[(None|[A-Z0-9]+-[0-9]+|#[0-9]+|https:\\/\\/nvbugs\\/[0-9]+)\\]) *(\\[[a-z0-9]+\\]) (([^ ].*)?[^ ])$"
          pr-body-regex: ""

      - name: PR Title Format Guide
        if: steps.check-pr-title.outcome == 'failure'
        run: |
          echo "::error::PR title format check failed."
          echo "Expected PR title format:"
          echo "  [JIRA ticket/NVBugs ID/GitHub issue/None][type] Summary"
          echo ""
          echo "Valid ticket formats:"
          echo "  - JIRA ticket: [TRTLLM-1234] or [FOOBAR-123] for other FOOBAR project"
          echo "  - NVBugs ID: [https://nvbugs/1234567]"
          echo "  - GitHub issue: [#1234]"
          echo "  - No ticket: [None]"
          echo ""
          echo "Valid types (lowercase): [fix], [feat], [doc], [infra], [chore], etc."
          echo ""
          echo "Examples:"
          echo "  - [TRTLLM-1234][feat] Add new feature"
          echo "  - [https://nvbugs/1234567][fix] Fix some bugs"
          echo "  - [#1234][doc] Update documentation"
          echo "  - [None][chore] Minor clean-up"
          exit 1

  check-pr-body-checklist:
    name: Check PR Checklist Resolution
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Validate PR Checklist
        env:
          PR_BODY: ${{ github.event.pull_request.body }}
          ENFORCE_PR_HAS_CHECKLIST: false
        run: python .github/scripts/pr_checklist_check.py
