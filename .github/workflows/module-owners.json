{"feature request": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "new model": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Inference runtime": ["funatiq", "pcastonguay", "Shixiaowei02", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "schetlur-nv", "dcampora"], "Triton backend": ["Tabrizian", "pcastonguay", "schetlur-nv"], "LLM API": ["Superjomn", "s<PERSON><PERSON>", "nv-guomingz", "litao<PERSON><PERSON>", "QiJune"], "KV-Cache Management": ["thor<PERSON><PERSON><PERSON>", "schetlur-nv"], "Low Precision": ["Tracin", "nv-guomingz", "Naveassaf"], "Speculative Decoding": ["yweng0828", "lfr-0531"], "Customized kernels": ["lowsfer", "PerkzZheng", "jdemouth-nvidia"], "General perf": ["kaiyux", "<PERSON><PERSON><PERSON>", "hypdeb"], "Lora/P-tuning": ["<PERSON><PERSON><PERSON>", "Naveassaf"], "Disaggregated serving": ["Shixiaowei02", "joyang-nv", "chuangz0", "schetlur-nv"], "Doc": ["nv-guomingz"], "Decoding": ["dcampora", "lfr-0531", "Naveassaf", "s<PERSON><PERSON>", "yweng0828"], "Memory": ["litao<PERSON><PERSON>", "peaceh-nv"], "Installation": ["hchings", "Superjomn", "nv-guomingz", "QiJune"]}